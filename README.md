# 战地五得分板信息OCR工具

## 主要功能

### 1. 裁剪出正确的得分板
- 利用机器学习自动根据不同分辨率和不同比例的全屏得分板截图进行裁剪。
- 如果可能，希望使其更通用，能够对任意的得分板图片进行识别。

### 2. 进一步提取每一项信息，包括
- 是否为小队长
- 击杀人数
- 兵种状态

## 得分板特性分析（基于硫磺岛）

### 1. 两队的得分板的左上角都有对应国家的国旗，能用于定位
- 国旗之间的距离就是各自得分板的宽度。
- 高度切割为难点，根据1280×720分辨率下所截取出的得分板进行分析得出：
  - 从得分板顶部开始，一共32行。
  - 第5、11、17、23、29行为14像素。
  - 其余奇数行为15像素，偶数行为16像素。

### 2. 得分板的表头有一些文字能确定不同信息的切割宽度
例如：
- “#1队”末尾到“兵种”开头为玩家等级与玩家ID的宽度。
- “兵种”的“种”字的宽度为玩家兵种的宽度。
- “兵种”末尾到“K”末尾为玩家击杀数的宽度。
- “K”末尾到“D”末尾为玩家死亡数的宽度。
- 对于二队也是一样的情况。

###
在table里收集了长度为1920*1080 2560*1410 3440*1410的得分表图片
对于他们的分割策略为，均以坐上角为坐标原点，向下向右为正
1920*1080
team1 
国旗 98,151 到 175,228
第一行从 195,212 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
玩家id列 334 到 334+364
队长标记列 698 到 698+21
兵种标记列 730 到 730+19
击杀数标记列 760 到 760+18
被击杀数标记列 796 到 796+18
team2 国旗 976,151 到 1053,228
第一行从 195,212 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
其余起始点为team1 加上 878
