import cv2
import os
import random
import string
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image  # 用于 torchvision.transforms (Image类)
from tqdm import tqdm  # 用于显示进度条


# --- 辅助函数：生成随机文件名 (沿用之前脚本的) ---
def generate_random_filename(length=9, extension='.jpg'):
    """生成指定长度的随机字母文件名。"""
    letters = string.ascii_lowercase
    random_name = ''.join(random.choice(letters) for i in range(length))
    return random_name + extension


# --- 1. 复制你的CNN模型架构 (与训练代码完全一致!) ---
class BattlefieldCNN(nn.Module):
    def __init__(self, num_classes=9):
        super(BattlefieldCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.<PERSON><PERSON>ool2d(2),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2)
        )
        self.classifier = nn.Sequential(
            nn.<PERSON>ten(),
            # 线性层输入维度根据新的 20x25 尺寸计算
            # 20x25 -> Conv(padding=1) -> 16x20x25 -> MaxPool(2) -> 16x10x12
            # -> Conv(padding=1) -> 32x10x12 -> MaxPool(2) -> 32x5x6
            # 展平后为 32 * 5 * 6 = 960
            nn.Linear(32 * 5 * 6, 64),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x


# --- 2. 模型路径和类别映射 ---
model_path = 'new_battlefield_arms_model.pth'  # 使用新训练的模型
# 类别映射：顺序必须与你模型训练时的输出顺序一致
class_names = ['tk', 'fj', 'ylb', 'zyb', 'tjb', 'zcb', 'dd', 'sw', 'kb']

# --- 3. 图像预处理定义 (与训练时保持一致) ---
# 训练时图片被 resize 为 20x25 (高x宽)
# transforms.Resize 期望 (height, width)
input_image_size_for_model = (20, 25)

transform_for_inference = transforms.Compose([
    transforms.ToPILImage(),  # OpenCV 读取是 NumPy 数组，需要转换为 PIL Image
    transforms.Resize(input_image_size_for_model),  # 强制 Resize 到 20x25
    transforms.ToTensor(),  # 将 PIL Image (0-255) 转换为 Tensor (0.0-1.0), 并调整维度为 CHW
])

# --- 4. 设置设备 (GPU 如果可用，否则 CPU) ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"将使用设备: {device}")

# --- 5. 加载模型 ---
try:
    model = BattlefieldCNN(num_classes=len(class_names))
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()  # 设置为评估模式
    print(f"模型 '{model_path}' 加载成功。")
except Exception as e:
    print(f"错误：加载模型失败。请确保模型路径正确，并且 'BattlefieldCNN' 的定义与 '{model_path}' 中的模型架构完全一致。")
    print(f"详细错误: {e}")
    exit()

# --- 6. 目录设置 ---
test_input_dir = 'test'  # 测试图片文件夹
output_test_dir = 'test_results'  # 结果保存文件夹

os.makedirs(output_test_dir, exist_ok=True)
print(f"测试结果将保存到 '{output_test_dir}' 目录下。")

# 获取所有支持的图片文件列表
test_image_files = []
valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')

try:
    for f in os.listdir(test_input_dir):
        if os.path.isfile(os.path.join(test_input_dir, f)) and f.lower().endswith(valid_extensions):
            test_image_files.append(f)
    test_image_files.sort()  # 按文件名排序
except FileNotFoundError:
    print(f"错误：测试图片目录 '{test_input_dir}' 不存在。请确保它与脚本在同一目录下。")
    exit()

if not test_image_files:
    print(f"在目录 '{test_input_dir}' 中没有找到任何支持的图片文件。")
    exit()

print(f"\n将在 '{test_input_dir}' 中找到以下图片进行测试：")
for fname in test_image_files:
    print(f"- {fname}")

# --- 7. 遍历并处理每个测试图片 ---
for filename in tqdm(test_image_files, desc="处理测试图片"):
    current_image_path = os.path.join(test_input_dir, filename)

    img = cv2.imread(current_image_path)
    if img is None:
        tqdm.write(f"警告：无法读取图片 '{current_image_path}'。跳过此图片。")
        continue

    # 克隆图片用于绘制结果
    img_with_results = img.copy()

    image_width = img.shape[1]

    # --- 定义通用变量，根据图片宽度赋值 (与之前的裁剪脚本逻辑一致) ---
    offset = 0
    player_rows_start_y = 0
    row_height_odd = 0
    row_height_even = 0
    # X坐标范围字典，用于获取队长和兵种列的X坐标
    # 键是 'captain' 或 'class'，值是 (x_start, x_end)
    column_x_ranges = {}

    if image_width == 1920:
        offset = 882
        player_rows_start_y = 212
        row_height_odd = 22
        row_height_even = 24
        # 队长列 Team1: (698, 719), Team2: (698+offset, 719+offset)
        # 兵种列 Team1: (730, 749), Team2: (730+offset, 749+offset)
        column_x_ranges = {
            'team1_captain': (698, 719),
            'team1_class': (730, 749),
            'team2_captain': (698 + offset, 719 + offset),
            'team2_class': (730 + offset, 749 + offset),
        }
    elif image_width == 2560:
        offset = 1017
        player_rows_start_y = 327
        row_height_odd = 25
        row_height_even = 28
        column_x_ranges = {
            'team1_captain': (978, 1001),
            'team1_class': (1015, 1035),
            'team2_captain': (978 + offset, 1001 + offset),
            'team2_class': (1015 + offset, 1035 + offset),
        }
    elif image_width == 3440:
        offset = 1017
        player_rows_start_y = 327
        row_height_odd = 25
        row_height_even = 28
        x_offset_3440 = 440
        column_x_ranges = {
            'team1_captain': (978 + x_offset_3440, 1001 + x_offset_3440),
            'team1_class': (1015 + x_offset_3440, 1035 + x_offset_3440),
            'team2_captain': (978 + x_offset_3440 + offset, 1001 + x_offset_3440 + offset),
            'team2_class': (1015 + x_offset_3440 + offset, 1035 + x_offset_3440 + offset),
        }
    else:
        tqdm.write(f"警告：图片 '{filename}' 具有不支持的宽度 {image_width}。跳过此图片。")
        continue

    current_y_for_rows = player_rows_start_y
    for row_idx in range(32):  # 总共32行
        row_height = row_height_odd if (row_idx + 1) % 2 == 1 else row_height_even
        y_start = current_y_for_rows
        y_end = current_y_for_rows + row_height

        # 处理 Team 1
        team1_captain_x_end = column_x_ranges['team1_captain'][1]
        team1_class_x_start = column_x_ranges['team1_class'][0]
        team1_class_x_end = column_x_ranges['team1_class'][1]

        # 兵种图标裁剪区域 Team 1
        class_icon_team1 = img[y_start:y_end, team1_class_x_start:team1_class_x_end]

        # 模型推理 Team 1
        if class_icon_team1.shape[0] > 0 and class_icon_team1.shape[1] > 0:  # 确保裁剪区域有效
            img_rgb_team1 = cv2.cvtColor(class_icon_team1, cv2.COLOR_BGR2RGB)
            input_tensor_team1 = transform_for_inference(img_rgb_team1).unsqueeze(0).to(device)

            with torch.no_grad():
                outputs_team1 = model(input_tensor_team1)
                probabilities_team1 = torch.softmax(outputs_team1, dim=1)
                predicted_index_team1 = torch.argmax(probabilities_team1, dim=1).item()
                predicted_class_name_team1 = class_names[predicted_index_team1]
                confidence_team1 = probabilities_team1[0][predicted_index_team1].item() * 100
        else:
            predicted_class_name_team1 = "N/A"
            confidence_team1 = 0.0

        # 绘制结果到 Team 1 区域
        # 绘制位置：队长标记列结束点 到 兵种列起始点 之间
        draw_x_start_team1 = team1_captain_x_end + 2  # 从队长图标右侧稍微偏移
        draw_x_end_team1 = team1_class_x_start - 2  # 到兵种图标左侧稍微偏移

        if draw_x_end_team1 > draw_x_start_team1:  # 确保有绘制空间
            text_to_draw_team1 = f"{predicted_class_name_team1}"  # ({confidence_team1:.0f}%)
            # 获取文字大小
            (text_width, text_height), baseline = cv2.getTextSize(text_to_draw_team1, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)

            # 计算文字和背景矩形的放置位置
            # 尝试居中放置在 draw_x_start_team1 和 draw_x_end_team1 之间
            center_x_team1 = (draw_x_start_team1 + draw_x_end_team1) // 2
            text_x_team1 = center_x_team1 - text_width // 2

            text_y_team1 = y_start + row_height // 2 + text_height // 2  # 垂直居中

            # 绘制白色背景矩形
            # 确保矩形不会超出图片边界
            rect_x1_team1 = max(0, text_x_team1 - 2)
            rect_y1_team1 = max(0, text_y_team1 - text_height - 2)
            rect_x2_team1 = min(img_with_results.shape[1], text_x_team1 + text_width + 2)
            rect_y2_team1 = min(img_with_results.shape[0], text_y_team1 + baseline + 2)

            cv2.rectangle(img_with_results, (rect_x1_team1, rect_y1_team1),
                          (rect_x2_team1, rect_y2_team1), (255, 255, 255), -1)  # -1 表示填充

            # 绘制红色文字
            cv2.putText(img_with_results, text_to_draw_team1, (text_x_team1, text_y_team1),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1, cv2.LINE_AA)

        # 处理 Team 2 (逻辑与 Team 1 相同，只是X坐标偏移)
        team2_captain_x_end = column_x_ranges['team2_captain'][1]
        team2_class_x_start = column_x_ranges['team2_class'][0]
        team2_class_x_end = column_x_ranges['team2_class'][1]

        # 兵种图标裁剪区域 Team 2
        class_icon_team2 = img[y_start:y_end, team2_class_x_start:team2_class_x_end]

        # 模型推理 Team 2
        if class_icon_team2.shape[0] > 0 and class_icon_team2.shape[1] > 0:  # 确保裁剪区域有效
            img_rgb_team2 = cv2.cvtColor(class_icon_team2, cv2.COLOR_BGR2RGB)
            input_tensor_team2 = transform_for_inference(img_rgb_team2).unsqueeze(0).to(device)

            with torch.no_grad():
                outputs_team2 = model(input_tensor_team2)
                probabilities_team2 = torch.softmax(outputs_team2, dim=1)
                predicted_index_team2 = torch.argmax(probabilities_team2, dim=1).item()
                predicted_class_name_team2 = class_names[predicted_index_team2]
                confidence_team2 = probabilities_team2[0][predicted_index_team2].item() * 100
        else:
            predicted_class_name_team2 = "N/A"
            confidence_team2 = 0.0

        # 绘制结果到 Team 2 区域
        draw_x_start_team2 = team2_captain_x_end + 2
        draw_x_end_team2 = team2_class_x_start - 2

        if draw_x_end_team2 > draw_x_start_team2:
            text_to_draw_team2 = f"{predicted_class_name_team2}"  # ({confidence_team2:.0f}%)
            (text_width, text_height), baseline = cv2.getTextSize(text_to_draw_team2, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)

            center_x_team2 = (draw_x_start_team2 + draw_x_end_team2) // 2
            text_x_team2 = center_x_team2 - text_width // 2

            text_y_team2 = y_start + row_height // 2 + text_height // 2

            rect_x1_team2 = max(0, text_x_team2 - 2)
            rect_y1_team2 = max(0, text_y_team2 - text_height - 2)
            rect_x2_team2 = min(img_with_results.shape[1], text_x_team2 + text_width + 2)
            rect_y2_team2 = min(img_with_results.shape[0], text_y_team2 + baseline + 2)

            cv2.rectangle(img_with_results, (rect_x1_team2, rect_y1_team2),
                          (rect_x2_team2, rect_y2_team2), (255, 255, 255), -1)

            cv2.putText(img_with_results, text_to_draw_team2, (text_x_team2, text_y_team2),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1, cv2.LINE_AA)

        current_y_for_rows = y_end  # 更新到下一行的起始Y坐标

    # 保存结果图片
    base_name = os.path.splitext(filename)[0]
    output_filename = f"{base_name}_result.jpg"
    cv2.imwrite(os.path.join(output_test_dir, output_filename), img_with_results)

print(f"\n所有测试图片处理完毕。结果已保存到 '{output_test_dir}'。")