import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import cv2
from sklearn.model_selection import train_test_split

print(f"CUDA 是否可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA 设备数量: {torch.cuda.device_count()}")
    print(f"当前 CUDA 设备名称: {torch.cuda.get_device_name(0)}")
    print(f"PyTorch 编译时支持的 CUDA 版本: {torch.version.cuda}")
    print(f"cuDNN 是否可用: {torch.backends.cudnn.is_available()}")
else:
    print("CUDA 不可用，PyTorch 将使用 CPU。")

# 定义类别
classes = ['tk', 'fj', 'ylb', 'zyb', 'tjb', 'zcb', 'dd', 'sw', 'kb']
class_to_idx = {cls: i for i, cls in enumerate(classes)}


# 自定义数据集
class BattlefieldDataset(Dataset):
    def __init__(self, images, labels, transform=None):
        self.images = images
        self.labels = labels
        self.transform = transform

    def __len__(self):
        return len(self.images)

    def __getitem__(self, idx):
        image = self.images[idx]
        label = self.labels[idx]
        if self.transform:
            image = self.transform(image)
        return image, label


# 数据加载和预处理
def load_data():
    images = []
    labels = []
    # 从 'Addition_Classified' 文件夹加载数据 (因为你打算用增量数据集训练)
    # 如果要用原始 Classified 训练，将 input_base_dir 改为 'Classified'
    input_base_dir = 'Addition_Classified'

    for cls in classes:
        folder_path = os.path.join(input_base_dir, cls)
        if not os.path.exists(folder_path):
            print(f"警告: 找不到文件夹 {folder_path}，跳过此类别。")
            continue

        print(f"正在加载类别: {cls} from {folder_path}")
        for filename in os.listdir(folder_path):
            # 确保不处理 'low' 这样的子文件夹，只处理图片文件
            if os.path.isfile(os.path.join(folder_path, filename)) and \
                    filename.lower().endswith(('.png', '.jpg', '.jpeg')):

                img_path = os.path.join(folder_path, filename)

                # 读取图片并调整大小为 25x20 (宽x高)，即长20像素宽25像素
                img = cv2.imread(img_path)
                if img is None:
                    print(f"警告: 无法读取图片 {img_path}，跳过。")
                    continue

                # 将图片尺寸统一为 25x20 (宽度 x 高度)
                # cv2.resize 参数顺序是 (width, height)
                img = cv2.resize(img, (25, 20))

                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # OpenCV默认是BGR，转为RGB
                images.append(img)
                labels.append(class_to_idx[cls])

    if not images:
        print(f"错误: 没有从 '{input_base_dir}' 目录加载到任何图片。请检查路径和图片是否存在。")
        exit()

    # 转换为numpy数组，并进行归一化到 [0.0, 1.0]
    images = np.array(images, dtype=np.float32) / 255.0
    labels = np.array(labels)

    print(f"成功加载 {len(images)} 张图片。")
    return images, labels


# 定义CNN模型
class BattlefieldCNN(nn.Module):
    def __init__(self, num_classes=9):
        super(BattlefieldCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2)
        )
        self.classifier = nn.Sequential(
            nn.Flatten(),
            # 线性层输入维度根据新的 20x25 尺寸计算
            # 20x25 -> Conv(padding=1) -> 16x20x25 -> MaxPool(2) -> 16x10x12
            # -> Conv(padding=1) -> 32x10x12 -> MaxPool(2) -> 32x5x6
            # 展平后为 32 * 5 * 6 = 960
            nn.Linear(32 * 5 * 6, 64),  # <-- 关键修改点：这里是 960
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x


# 主函数
def main():
    # 加载数据
    print("加载数据...")
    images, labels = load_data()

    # 划分训练集和测试集
    # stratify=labels 确保训练集和测试集中各类别样本比例与总数据集一致
    X_train, X_test, y_train, y_test = train_test_split(
        images, labels, test_size=0.2, random_state=42, stratify=labels
    )

    print(f"训练集大小: {len(X_train)} 张图片")
    print(f"测试集大小: {len(X_test)} 张图片")

    # 数据转换 (NumPy HWC 转换为 PyTorch CHW Tensor)
    # 注意：这里没有使用 torchvision.transforms.ToTensor()，因为它期望 PIL Image
    # 而是直接用 Lambda 将 NumPy 数组手动转置和转换为 Tensor
    tensor_transform = transforms.Lambda(lambda x: torch.from_numpy(x.transpose(2, 0, 1)))

    # 创建数据集和数据加载器
    train_dataset = BattlefieldDataset(
        X_train, y_train,
        transform=tensor_transform
    )
    test_dataset = BattlefieldDataset(
        X_test, y_test,
        transform=tensor_transform
    )

    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 设置设备
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建模型
    model = BattlefieldCNN(num_classes=len(classes))
    model.to(device)

    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练模型
    num_epochs = 50
    print("开始训练...")
    for epoch in range(num_epochs):
        model.train()  # 设置模型为训练模式
        running_loss = 0.0
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)  # 将数据移到设备

            optimizer.zero_grad()  # 清空梯度
            outputs = model(inputs)  # 前向传播
            loss = criterion(outputs, labels)  # 计算损失
            loss.backward()  # 反向传播
            optimizer.step()  # 更新权重

            running_loss += loss.item()  # 累计损失

        # 每个epoch结束后评估模型
        model.eval()  # 设置模型为评估模式
        correct = 0
        total = 0
        with torch.no_grad():  # 推理时禁用梯度计算
            for inputs, labels in test_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                _, predicted = torch.max(outputs, 1)  # 获取最高概率的类别
                total += labels.size(0)
                correct += (predicted == labels).sum().item()  # 统计正确预测的数量

        accuracy = 100 * correct / total
        print(
            f"Epoch {epoch + 1}/{num_epochs}, "
            f"Loss: {running_loss / len(train_loader):.4f}, "
            f"Accuracy: {accuracy:.2f}%"
        )
    print("训练完成!")

    # 保存模型
    torch.save(model.state_dict(), "new_battlefield_arms_model.pth")
    print("模型已保存为 new_battlefield_arms_model.pth")


if __name__ == "__main__":
    main()