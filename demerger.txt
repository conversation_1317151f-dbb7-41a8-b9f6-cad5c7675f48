1920*1080
offset = 882
rectangles = [
    # Team1 国旗
    ((98, 151), (175, 228)),
    # Team2 国旗
    ((976, 151), (1053, 228)),
    # Team1 玩家ID列：x从334到698，y从212到947
    ((332, 212), (698, 947)),
    # Team1 队长标记列：x从698到719，y从212到947
    ((698, 212), (719, 947)),
    # Team1 兵种标记列：x从730到749，y从212到947 20
    ((730, 212), (749, 947)),
    # Team1 击杀数标记列：x从760到778，y从212到947
    ((750 , 212), (786 , 947)),
    # Team1 被击杀数标记列：x从796到814，y从212到947
    ((786 , 212), (822 , 947)),
    # Team2 玩家ID列：x从334+offset到698+offset，y从212到947
    ((332 + offset, 212), (698 + offset, 947)),
    # Team2 队长标记列：x从698+offset到719+offset，y从212到947
    ((698 + offset, 212), (719 + offset, 947)),
    # Team2 兵种标记列：x从730+offset到749+offset，y从212到947
    ((730 + offset, 212), (749 + offset, 947)),
    # Team2 击杀数标记列：x从760+offset到778+offset，y从212到947
    ((750 + offset, 212), (786 + offset, 947)),
    # Team2 被击杀数标记列：x从796+offset到814+offset，y从212到947
    ((786 + offset, 212), (822 + offset, 947))
]
1920*1080
team1
国旗 98,151 到 175,228
第一行从 195,212 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
玩家id列 332 到 332+366
队长标记列 698 到 719
兵种标记列 730 到 730+19
击杀数标记列 750 到 786
被击杀数标记列 786 到 822
team2 国旗 976,151 到 1053,228
第一行从 195,212 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
其余起始点为team1 加上 882
2560*1440
team1
国旗 285, 255 到 375, 345
第一行左上角为 399,327
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
玩家id列 558 到 978
队长标记列 978 到 1001
兵种标记列 1015 到 1035
击杀数标记列 1035 到 1080
被击杀数标记列 1080 到 1115
team2 国旗 1298, 255 到 1388, 345
第一行从 1416,327 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为22像素。
其余奇数行为22像素，偶数行为24像素。
其余起始点为team1 加上 1,017
offset = 1,017
rectangles = [
]
2560*1440
team1
国旗 285, 255 到 375, 345
第一行左上角为 399,327
从得分板顶部开始，一共32行。
第5、11、17、23、29行为25像素。
其余奇数行为25像素，偶数行为28像素。
玩家id列 558 到 978
队长标记列 978 到 1001
兵种标记列 1015 到 1035
击杀数标记列 1035 到 1080
被击杀数标记列 1080 到 1115
team2 国旗 1298, 255 到 1388, 345
第一行从 1416,327 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为25像素。
其余奇数行为25像素，偶数行为28像素。
其余起始点为team1 加上 1,017
offset = 1017
rectangles = [
    # Team1 国旗
    ((285, 255), (375, 345)),
    # Team2 国旗
    ((1298, 255), (1388, 345)),

    # Team1 玩家ID列
    ((558, 327), (978, 1175)),
    # Team1 队长标记列
    ((978, 327), (1001, 1175)),
    # Team1 兵种标记列 20
    ((1015, 327), (1035, 1175)),
    # Team1 击杀数标记列
    ((1035, 327), (1080, 1175)),
    # Team1 被击杀数标记列
    ((1080, 327), (1115, 1175)),

    # Team2 玩家ID列
    ((558 + offset, 327), (978 + offset, 1175)),
    # Team2 队长标记列
    ((978 + offset, 327), (1001 + offset, 1175)),
    # Team2 兵种标记列
    ((1015 + offset, 327), (1035 + offset, 1175)),
    # Team2 击杀数标记列
    ((1035 + offset, 327), (1080 + offset, 1175)),
    # Team2 被击杀数标记列
    ((1080 + offset, 327), (1115 + offset, 1175))
]
3440*1440
team1
国旗 725, 255 到 815, 345
第一行左上角为 839,327
从得分板顶部开始，一共32行。
第5、11、17、23、29行为25像素。
其余奇数行为25像素，偶数行为28像素。
玩家id列 998 到 1418
队长标记列 1418 到 1441
兵种标记列 1455 到 1475
击杀数标记列 1475 到 1520
被击杀数标记列 1520 到 1555

team2 国旗 1738, 255 到 1828, 345
第一行从 1856,327 开始
从得分板顶部开始，一共32行。
第5、11、17、23、29行为25像素。
其余奇数行为25像素，偶数行为28像素。
其余起始点为team1 加上 1,017 (即，Team2的X坐标 = Team1对应的X坐标 + 1017)

offset = 1017
rectangles = [
    # Team1 国旗
    ((725, 255), (815, 345)),
    # Team2 国旗
    ((1738, 255), (1828, 345)),

    # Team1 玩家ID列
    ((998, 327), (1418, 1175)),
    # Team1 队长标记列
    ((1418, 327), (1441, 1175)),
    # Team1 兵种标记列 20
    ((1455, 327), (1475, 1175)),
    # Team1 击杀数标记列
    ((1475, 327), (1520, 1175)),
    # Team1 被击杀数标记列
    ((1520, 327), (1555, 1175)),

    # Team2 玩家ID列 (X坐标为 Team1 的X + offset)
    ((998 + offset, 327), (1418 + offset, 1175)), # 计算后为 ((2015, 327), (2435, 1175))
    # Team2 队长标记列
    ((1418 + offset, 327), (1441 + offset, 1175)), # 计算后为 ((2435, 327), (2458, 1175))
    # Team2 兵种标记列
    ((1455 + offset, 327), (1475 + offset, 1175)), # 计算后为 ((2472, 327), (2492, 1175))
    # Team2 击杀数标记列
    ((1475 + offset, 327), (1520 + offset, 1175)), # 计算后为 ((2492, 327), (2537, 1175))
    # Team2 被击杀数标记列
    ((1520 + offset, 327), (1555 + offset, 1175))  # 计算后为 ((2537, 327), (2572, 1175))
]