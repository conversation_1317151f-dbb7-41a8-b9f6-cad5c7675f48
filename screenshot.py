import time
import pygetwindow as gw
import pydirectinput
import sys
import win32api  # 用于 DPI 和窗口调整
import win32gui
import win32con
import ctypes  # 用于 RECT 结构体

# 定义 RECT 结构体
class RECT(ctypes.Structure):
    _fields_ = [
        ("left", ctypes.c_long),
        ("top", ctypes.c_long),
        ("right", ctypes.c_long),
        ("bottom", ctypes.c_long)
    ]

# 定义 AdjustWindowRect 函数
def AdjustWindowRect(rect, style, menu):
    """调整窗口矩形以适应客户区域"""
    # 返回值为非零表示成功
    return ctypes.windll.user32.AdjustWindowRect(ctypes.byref(rect), style, menu)

# 定义全局变量
_pywin32_available = True  # 或者 False，根据实际情况

# 新增：主流分辨率列表
MAIN_RESOLUTIONS = [
    (3440, 1440),
]

# --- 辅助函数 (聚焦, 刷新, 清理 - 基本不变) ---
def ensure_window_focus(window_title):
    """尝试查找并聚焦指定的窗口。"""
    global _pywin32_available  # 引用全局标记
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            print(f"未找到窗口: {window_title}")
            return False
        window = windows[0]
        if window.isMinimized:
            print(f"窗口 '{window_title}' 已最小化，尝试恢复...")
            window.restore()
            time.sleep(0.5)
        print(f"尝试激活窗口: '{window_title}'")
        activated = False
        try:
            window.activate()
            time.sleep(0.2)
            if gw.getActiveWindow() and gw.getActiveWindow().title == window_title:
                activated = True
        except Exception as e_activate:
            print(f"  window.activate() 失败: {e_activate}")
        if not activated and _pywin32_available:
            try:
                hwnd = win32gui.FindWindow(None, window_title)
                if hwnd:
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.1)
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, 0, 0,
                                          win32con.SWP_NOMOVE | win32con.SWP_NOSIZE | win32con.SWP_SHOWWINDOW)
            except Exception as e_win32:
                print(f"  win32 操作错误: {e_win32}")
        time.sleep(0.5)
        active_window = gw.getActiveWindow()
        if active_window and active_window.title == window_title:
            print(f"窗口 '{window_title}' 已激活。")
            return True
        return False
    except Exception as e:
        print(f"错误: {e}")
        return False

# --- 主要截图函数 ---
def take_battlefield_screenshot(window_title='Battlefield™ V'):
    tab_key_pressed = False
    try:
        for resolution in MAIN_RESOLUTIONS:
            target_client_width, target_client_height = resolution
            print(f"\n处理分辨率: {target_client_width}x{target_client_height}")
            if not ensure_window_focus(window_title):
                print(f"警告：未能聚焦窗口 '{window_title}'，跳过此分辨率。")
                continue
            bfv_windows = gw.getWindowsWithTitle(window_title)
            if not bfv_windows:
                print(f"错误：未找到窗口 '{window_title}'。")
                continue
            bfv_window = bfv_windows[0]
            print(f"已找到窗口: {bfv_window.title}")
            print(f"目标客户端尺寸: {target_client_width}x{target_client_height}")
            if _pywin32_available and sys.platform == 'win32':
                try:
                    hwnd = win32gui.FindWindow(None, window_title)
                    if hwnd:
                        # 直接使用您指定的缩放因子 1.5
                        scale_factor = 1  # 手动设置为1.5，根据您的系统缩放
                        adjusted_width = int(target_client_width * scale_factor)
                        adjusted_height = int(target_client_height * scale_factor)
                        adjust_rect = RECT()
                        adjust_rect.left = 0
                        adjust_rect.top = 0
                        adjust_rect.right = adjusted_width
                        adjust_rect.bottom = adjusted_height
                        style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
                        AdjustWindowRect(adjust_rect, style, False)
                        new_width = adjust_rect.right - adjust_rect.left
                        new_height = adjust_rect.bottom - adjust_rect.top
                        win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, new_width, new_height, win32con.SWP_NOMOVE)
                        time.sleep(0.5)
                        print(f"窗口调整为: {new_width}x{new_height}")
                except Exception as e_adjust:
                    print(f"调整窗口错误: {e_adjust}")
                    bfv_window.resizeTo(target_client_width, target_client_height)
                    time.sleep(0.5)
            else:
                bfv_window.resizeTo(target_client_width, target_client_height)
                time.sleep(0.5)
            print(f"\n准备按键为分辨率 {target_client_width}x{target_client_height}...")
            print("按下 F1 键...")
            pydirectinput.press('f1')
            time.sleep(0.2)
            # 随机决定是否按下 F5 键，概率为 50%
            import random
            if random.random() < 0.5:
                print("按下 F5 键...")
                pydirectinput.press('f5')
            time.sleep(0.2)
            print("按下 Tab 键 (按住)...")
            pydirectinput.keyDown('tab')
            tab_key_pressed = True
            time.sleep(2)  # 等待 UI 响应
            print("按下 F12 键...")
            pydirectinput.press('f12')
            time.sleep(1)  # 等待截图触发
            print("释放 Tab 键...")
            pydirectinput.keyUp('tab')
            tab_key_pressed = False
            print(f"分辨率 {target_client_width}x{target_client_height} 的操作完成。等待1秒再处理下一个...")
            time.sleep(1)  # 暂停以避免游戏崩溃
    except Exception as e_main:
        print(f"错误: {e_main}")
    finally:
        if tab_key_pressed:
            pydirectinput.keyUp('tab')

# 主入口，添加循环每30秒运行一次
if __name__ == "__main__":
    while True:
        try:
            print("=" * 30)
            print("开始测试多分辨率截图流程")
            print("=" * 30)
            take_battlefield_screenshot()
            print("\n" + "=" * 30)
            print("操作完成。等待30秒后再次运行...")
            print("=" * 30)
            time.sleep(30)  # 每30秒运行一次
        except KeyboardInterrupt:
            print("用户中断（Ctrl+C），停止运行。")
            break
        except Exception as e_loop:
            print(f"循环错误: {e_loop}")
            print("等待10秒后重试...")
            time.sleep(10)  # 如果出错，等待10秒再重试，避免无限循环崩溃