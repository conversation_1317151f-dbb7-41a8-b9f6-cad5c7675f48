import cv2
import os
import random
import string

# --- 辅助函数：生成随机文件名 ---
def generate_random_filename(length=9, extension='.jpg'):
    """生成指定长度的随机字母文件名。"""
    letters = string.ascii_lowercase
    random_name = ''.join(random.choice(letters) for i in range(length))
    return random_name + extension

# --- 输入目录设置 ---
input_dir = 'table' # 图片现在位于 'table/' 子目录中

# --- 输出目录设置 ---
output_base_dir = 'output_cropped_images'
os.makedirs(output_base_dir, exist_ok=True)
print(f"所有裁剪图片将保存到 '{output_base_dir}' 目录下。")

# --- 新增参数：是否启用绘制框线功能 ---
# 设置为 True 会在图片上绘制红色框线并为每张原图生成一个对应的 'output_with_boxes_<原文件名>.jpg'
# 设置为 False 则跳过绘制步骤，只进行图片裁剪
enable_drawing = False

# --- 新增参数：是否裁剪特定类型的信息 ---
# 将对应键的值设置为 True 以启用裁剪，False 则禁用
# 默认全部启用
crop_info_settings = {
    'team1_flag': True,     # 队伍1国旗
    'team2_flag': True,     # 队伍2国旗
    'player_id': False,      # 玩家ID
    'captain': True,        # 队长标记
    'class': True,          # 兵种标记
    'kills': False,          # 击杀数
    'deaths': False,         # 被击杀数
}
print("\n裁剪设置:")
for key, value in crop_info_settings.items():
    print(f"- {key}: {'启用' if value else '禁用'}")


# 获取所有支持的图片文件列表
image_files_to_process = []
valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif') # 支持的图片格式

try:
    for f in os.listdir(input_dir):
        if os.path.isfile(os.path.join(input_dir, f)) and f.lower().endswith(valid_extensions):
            image_files_to_process.append(f)
    image_files_to_process.sort() # 按文件名排序，实现“按顺序”处理
except FileNotFoundError:
    print(f"错误：输入目录 '{input_dir}' 不存在。请确保它与脚本在同一目录下。")
    exit()
except Exception as e:
    print(f"读取目录 '{input_dir}' 时发生错误: {e}")
    exit()

if not image_files_to_process:
    print(f"在目录 '{input_dir}' 中没有找到任何支持的图片文件 ({', '.join(valid_extensions)})。")
    exit()

print(f"\n将在 '{input_dir}' 中找到以下图片进行处理：")
for fname in image_files_to_process:
    print(f"- {fname}")

# --- 遍历并处理每个图片文件 ---
for filename in image_files_to_process:
    current_image_path = os.path.join(input_dir, filename)
    print(f"\n--- 正在处理图片: {current_image_path} ---")

    img = cv2.imread(current_image_path)

    # 检查图片是否读取成功，如果失败则跳过当前图片
    if img is None:
        print(f"警告：无法读取图片 '{current_image_path}'。可能文件已损坏或无法访问。跳过此图片。")
        continue

    # 如果启用绘制功能，则复制一份图片用于绘制，避免影响裁剪操作
    if enable_drawing:
        img_for_drawing = img.copy()
    else:
        img_for_drawing = None # 不启用绘制时，此变量不需要

    # 获取图片宽度
    image_width = img.shape[1]

    # --- 定义通用变量，根据图片宽度赋值 (这些变量在每次循环中重新定义) ---
    offset = 0
    rectangles_to_draw = [] # 用于绘制的矩形框 (可视化用)
    static_crops_info = [] # 用于裁剪的固定区域信息 (例如国旗)
    player_column_crops_info = [] # 用于裁剪的玩家列表列信息 (包含X坐标和通用类型名)

    x_min = 0  # 表格左侧起始X坐标，用于绘制行分隔线
    x_max = 0  # 表格右侧结束X坐标，用于绘制行分隔线
    player_rows_start_y = 0  # 玩家列表行的起始Y坐标
    player_rows_end_y = 0  # 玩家列表行的结束Y坐标
    row_height_odd = 0  # 奇数行高度
    row_height_even = 0  # 偶数行高度

    # 根据图片宽度选择不同的分割策略
    if image_width == 1920:
        print("检测到分辨率：1920x1080")
        offset = 882
        player_rows_start_y = 212
        row_height_odd = 22
        row_height_even = 24
        player_rows_end_y = player_rows_start_y + (16 * row_height_odd) + (16 * row_height_even) # 736
        x_min = 195
        x_max = 822 + offset # Team2 被击杀数标记列结束点

        # 用于绘制的矩形框 (保持原样，用于可视化所有区域)
        rectangles_to_draw = [
            ((98, 151), (175, 228)), # Team1 国旗
            ((976, 151), (1053, 228)), # Team2 国旗
            ((332, player_rows_start_y), (698, player_rows_end_y)), # Team1 玩家ID列
            ((698, player_rows_start_y), (719, player_rows_end_y)), # Team1 队长标记列
            ((730, player_rows_start_y), (749, player_rows_end_y)), # Team1 兵种标记列
            ((750, player_rows_start_y), (786, player_rows_end_y)), # Team1 击杀数标记列
            ((786, player_rows_start_y), (822, player_rows_end_y)), # Team1 被击杀数标记列
            ((332 + offset, player_rows_start_y), (698 + offset, player_rows_end_y)), # Team2 玩家ID列
            ((698 + offset, player_rows_start_y), (719 + offset, player_rows_end_y)), # Team2 队长标记列
            ((730 + offset, player_rows_start_y), (749 + offset, player_rows_end_y)), # Team2 兵种标记列
            ((750 + offset, player_rows_start_y), (786 + offset, player_rows_end_y)), # Team2 击杀数标记列
            ((786 + offset, player_rows_start_y), (822 + offset, player_rows_end_y))  # Team2 被击杀数标记列
        ]

        # 用于裁剪的固定区域信息： (文件夹名, (x1, y1), (x2, y2))
        static_crops_info = [
            ('team1_flag', (98, 151), (175, 228)),
            ('team2_flag', (976, 151), (1053, 228))
        ]

        # 用于裁剪的玩家列表列信息： (通用文件夹名, x_start, x_end)
        player_column_crops_info = [
            ('player_id', 332, 698),           # Team1 玩家ID
            ('captain', 698, 719),             # Team1 队长
            ('class', 730, 749),               # Team1 兵种
            ('kills', 750, 786),               # Team1 击杀数
            ('deaths', 786, 822),              # Team1 被击杀数
            ('player_id', 332 + offset, 698 + offset), # Team2 玩家ID
            ('captain', 698 + offset, 719 + offset),   # Team2 队长
            ('class', 730 + offset, 749 + offset),     # Team2 兵种
            ('kills', 750 + offset, 786 + offset),     # Team2 击杀数
            ('deaths', 786 + offset, 822 + offset)      # Team2 被击杀数
        ]

    elif image_width == 2560:
        print("检测到分辨率：2560x1440")
        offset = 1017
        player_rows_start_y = 327
        row_height_odd = 25
        row_height_even = 28
        player_rows_end_y = player_rows_start_y + (16 * row_height_odd) + (16 * row_height_even) # 848
        x_min = 399
        x_max = 1115 + offset

        rectangles_to_draw = [
            ((285, 255), (375, 345)), # Team1 国旗
            ((1298, 255), (1388, 345)), # Team2 国旗
            ((558, player_rows_start_y), (978, player_rows_end_y)), # Team1 玩家ID列
            ((978, player_rows_start_y), (1001, player_rows_end_y)), # Team1 队长标记列
            ((1015, player_rows_start_y), (1035, player_rows_end_y)), # Team1 兵种标记列
            ((1035, player_rows_start_y), (1080, player_rows_end_y)), # Team1 击杀数标记列
            ((1080, player_rows_start_y), (1115, player_rows_end_y)), # Team1 被击杀数标记列
            ((558 + offset, player_rows_start_y), (978 + offset, player_rows_end_y)), # Team2 玩家ID列
            ((978 + offset, player_rows_start_y), (1001 + offset, player_rows_end_y)), # Team2 队长标记列
            ((1015 + offset, player_rows_start_y), (1035 + offset, player_rows_end_y)), # Team2 兵种标记列
            ((1035 + offset, player_rows_start_y), (1080 + offset, player_rows_end_y)), # Team2 击杀数标记列
            ((1080 + offset, player_rows_start_y), (1115 + offset, player_rows_end_y)) # Team2 被击杀数标记列
        ]

        static_crops_info = [
            ('team1_flag', (285, 255), (375, 345)),
            ('team2_flag', (1298, 255), (1388, 345))
        ]

        player_column_crops_info = [
            ('player_id', 558, 978),
            ('captain', 978, 1001),
            ('class', 1015, 1035),
            ('kills', 1035, 1080),
            ('deaths', 1080, 1115),
            ('player_id', 558 + offset, 978 + offset),
            ('captain', 978 + offset, 1001 + offset),
            ('class', 1015 + offset, 1035 + offset),
            ('kills', 1035 + offset, 1080 + offset),
            ('deaths', 1080 + offset, 1115 + offset)
        ]

    elif image_width == 3440:
        print("检测到分辨率：3440x1440")
        offset = 1017
        player_rows_start_y = 327
        row_height_odd = 25
        row_height_even = 28
        player_rows_end_y = player_rows_start_y + (16 * row_height_odd) + (16 * row_height_even) # 848
        x_offset_3440 = 440 # 3440x1440分辨率下，X轴坐标在2560x1440的基础上加上440
        x_min = 399 + x_offset_3440
        x_max = (1115 + x_offset_3440) + offset

        rectangles_to_draw = [
            ((285 + x_offset_3440, 255), (375 + x_offset_3440, 345)), # Team1 国旗
            ((1298 + x_offset_3440, 255), (1388 + x_offset_3440, 345)), # Team2 国旗
            ((558 + x_offset_3440, player_rows_start_y), (978 + x_offset_3440, player_rows_end_y)), # Team1 玩家ID列
            ((978 + x_offset_3440, player_rows_start_y), (1001 + x_offset_3440, player_rows_end_y)), # Team1 队长标记列
            ((1015 + x_offset_3440, player_rows_start_y), (1035 + x_offset_3440, player_rows_end_y)), # Team1 兵种标记列
            ((1035 + x_offset_3440, player_rows_start_y), (1080 + x_offset_3440, player_rows_end_y)), # Team1 击杀数标记列
            ((1080 + x_offset_3440, player_rows_start_y), (1115 + x_offset_3440, player_rows_end_y)), # Team1 被击杀数标记列
            ((558 + x_offset_3440 + offset, player_rows_start_y), (978 + x_offset_3440 + offset, player_rows_end_y)), # Team2 玩家ID列
            ((978 + x_offset_3440 + offset, player_rows_start_y), (1001 + x_offset_3440 + offset, player_rows_end_y)), # Team2 队长标记列
            ((1015 + x_offset_3440 + offset, player_rows_start_y), (1035 + x_offset_3440 + offset, player_rows_end_y)), # Team2 兵种标记列
            ((1035 + x_offset_3440 + offset, player_rows_start_y), (1080 + x_offset_3440 + offset, player_rows_end_y)), # Team2 击杀数标记列
            ((1080 + x_offset_3440 + offset, player_rows_start_y), (1115 + x_offset_3440 + offset, player_rows_end_y)) # Team2 被击杀数标记列
        ]

        static_crops_info = [
            ('team1_flag', (285 + x_offset_3440, 255), (375 + x_offset_3440, 345)),
            ('team2_flag', (1298 + x_offset_3440, 255), (1388 + x_offset_3440, 345))
        ]

        player_column_crops_info = [
            ('player_id', 558 + x_offset_3440, 978 + x_offset_3440),
            ('captain', 978 + x_offset_3440, 1001 + x_offset_3440),
            ('class', 1015 + x_offset_3440, 1035 + x_offset_3440),
            ('kills', 1035 + x_offset_3440, 1080 + x_offset_3440),
            ('deaths', 1080 + x_offset_3440, 1115 + x_offset_3440),
            ('player_id', 558 + x_offset_3440 + offset, 978 + x_offset_3440 + offset),
            ('captain', 978 + x_offset_3440 + offset, 1001 + x_offset_3440 + offset),
            ('class', 1015 + x_offset_3440 + offset, 1035 + x_offset_3440 + offset),
            ('kills', 1035 + x_offset_3440 + offset, 1080 + x_offset_3440 + offset),
            ('deaths', 1080 + x_offset_3440 + offset, 1115 + x_offset_3440 + offset)
        ]
    else:
        print(f"警告：图片 '{filename}' 具有不支持的宽度 {image_width}。当前支持 1920, 2560, 3440 宽度。跳过此图片。")
        continue # 跳过不支持分辨率的图片

    # --- 裁剪并保存固定区域图片 ---
    print("开始裁剪固定区域图片...")
    for folder_name, pt1, pt2 in static_crops_info:
        # 检查是否启用了此类型信息的裁剪
        if crop_info_settings.get(folder_name, False): # .get()确保即使键不存在也不会报错，并默认为False
            folder_path = os.path.join(output_base_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True) # 确保文件夹存在

            # 裁剪图片： img[y_start:y_end, x_start:x_end]
            cropped_img = img[pt1[1]:pt2[1], pt1[0]:pt2[0]]
            filename_cropped = generate_random_filename()
            file_path = os.path.join(folder_path, filename_cropped)
            cv2.imwrite(file_path, cropped_img)
            # print(f"保存：{file_path}") # 调试信息，如果裁剪图片很多可以注释掉
        else:
            print(f"裁剪 '{folder_name}' 被禁用，跳过。") # Informative message


    # --- 裁剪并保存玩家列表单元格图片 ---
    print("开始裁剪玩家列表单元格图片...")
    current_y_for_rows = player_rows_start_y
    for row_idx in range(32): # 总共32行
        # 计算当前行的Y轴范围
        row_height = row_height_odd if (row_idx + 1) % 2 == 1 else row_height_even
        y_start = current_y_for_rows
        y_end = current_y_for_rows + row_height

        for folder_name, x_start, x_end in player_column_crops_info:
            # 检查是否启用了此类型信息的裁剪
            if crop_info_settings.get(folder_name, False):
                folder_path = os.path.join(output_base_dir, folder_name)
                os.makedirs(folder_path, exist_ok=True) # 确保文件夹存在

                # 裁剪图片： img[y_start:y_end, x_start:x_end]
                cropped_cell = img[y_start:y_end, x_start:x_end]
                filename_cropped = generate_random_filename()
                file_path = os.path.join(folder_path, filename_cropped)
                cv2.imwrite(file_path, cropped_cell)
                # print(f"保存：{file_path}") # 调试信息，如果裁剪图片很多可以注释掉
            # else:
            #     # 避免输出过多信息，这里不打印每行每列的禁用提示
            #     pass

        current_y_for_rows = y_end # 更新到下一行的起始Y坐标

    print(f"图片 '{filename}' 的所有裁剪图片已保存。")

    # --- 根据 enable_drawing 参数决定是否绘制框线并保存 ---
    if enable_drawing:
        print("正在绘制框线...")
        # 绘制所有矩形框，颜色为红色 (BGR: 0, 0, 255)，线宽为1像素
        for pt1, pt2 in rectangles_to_draw:
            cv2.rectangle(img_for_drawing, pt1, pt2, (0, 0, 255), 1)

        # 添加行分隔线的绘制
        current_y = player_rows_start_y
        for row in range(1, 33):  # 行号从1到32
            cv2.line(img_for_drawing, (x_min, current_y), (x_max, current_y), (0, 0, 255), 1)
            if row % 2 == 1:  # 奇数行
                current_y += row_height_odd
            else:  # 偶数行
                current_y += row_height_even
        # 绘制最后一条线，表示列表的底部
        cv2.line(img_for_drawing, (x_min, player_rows_end_y), (x_max, player_rows_end_y), (0, 0, 255), 1)

        # 保存输出图片，文件名包含原图片名
        base_name = os.path.splitext(filename)[0] # 获取不带扩展名的文件名
        output_overlay_path = f'output_with_boxes_{base_name}.jpg'
        cv2.imwrite(output_overlay_path, img_for_drawing)
        print(f"包含绘制框线的输出图片已保存为 '{output_overlay_path}'。")

        # 可选：显示图片 (如果您在支持GUI的环境中运行)
        # cv2.imshow(f'Output Image with Boxes - {filename}', img_for_drawing)
        # cv2.waitKey(1) # 短暂显示，然后继续下一张 (如果有很多图片，可能卡顿)
    else:
        print("跳过绘制框线功能。")

# 循环结束后统一关闭所有OpenCV窗口 (如果开启了显示功能)
# if enable_drawing:
#    cv2.destroyAllWindows()

print("\n所有图片文件处理完毕。")