import random

import cv2
import os
import shutil  # 用于文件操作 (copy, move)
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image  # 用于 torchvision.transforms (Image类)
import string  # 用于 generate_random_filename
from tqdm import tqdm  # 导入 tqdm 库


# --- 辅助函数：生成随机文件名 (沿用之前脚本的) ---
def generate_random_filename(length=9, extension='.jpg'):
    """生成指定长度的随机字母文件名。"""
    letters = string.ascii_lowercase
    random_name = ''.join(random.choice(letters) for i in range(length))
    return random_name + extension


# --- 1. 复制你的CNN模型架构 (与训练代码完全一致!) ---
class BattlefieldCNN(nn.Module):
    def __init__(self, num_classes=9):
        super(BattlefieldCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2)
        )
        self.classifier = nn.Sequential(
            nn.Flatten(),
            # 确保这里的线性层输入维度与模型结构和输入尺寸相匹配
            # 14x14 -> Conv(padding=1) -> 16x14x14 -> MaxPool(2) -> 16x7x7
            # -> Conv(padding=1) -> 32x7x7 -> MaxPool(2) -> 32x3x3
            # 展平后为 32 * 3 * 3 = 288
            nn.Linear(32 * 3 * 3, 64),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x


# --- 2. 模型路径和类别映射 ---
model_path = 'battlefield_model.pth'  # 确保模型文件在脚本同目录下
# 类别映射：顺序必须与你模型训练时的输出顺序一致
class_names = ['tk', 'fj', 'ylb', 'zyb', 'tjb', 'zcb', 'dd', 'sw', 'kb']

# --- 3. 图像预处理定义 (与训练时保持一致) ---
# 训练时图片被 resize 为 14x14 (宽x高)
input_image_size = (14, 14)  # (width, height) for cv2.resize, (height, width) for transforms.Resize

transform = transforms.Compose([
    transforms.ToPILImage(),  # OpenCV 读取是 NumPy 数组，需要转换为 PIL Image
    transforms.Resize(input_image_size),  # 强制 Resize 到 14x14
    transforms.ToTensor(),  # 将 PIL Image (0-255) 转换为 Tensor (0.0-1.0), 并调整维度为 CHW
    # 注意: 训练代码实际并未应用 transforms.Normalize(mean=[0.5,0.5,0.5], std=[0.5,0.5,0.5])
    # 仅进行了 /255.0 归一化。ToTensor() 已经实现了 [0,1] 归一化。
])

# --- 4. 设置设备 (GPU 如果可用，否则 CPU) ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"将使用设备: {device}")

# --- 5. 加载模型 ---
try:
    # 实例化模型
    model = BattlefieldCNN(num_classes=len(class_names))
    # 将模型加载到正确的设备上
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()  # 设置为评估模式 (禁用 dropout, batch norm 等)
    print(f"模型 '{model_path}' 加载成功。")
except Exception as e:
    print(f"错误：加载模型失败。请确保模型路径正确，并且 'BattlefieldCNN' 的定义与 '{model_path}' 中的模型架构完全一致。")
    print(f"详细错误: {e}")
    exit()

# --- 6. 输入输出目录设置 ---
# 仅对 output_cropped_images/class 目录下的图片进行分类
cropped_images_root_dir = 'output_cropped_images'
target_classification_dir = os.path.join(cropped_images_root_dir, 'class')  # 指定要分类的子目录

# 分类结果保存的根目录，改为 'Classified'
classified_images_output_dir = 'Classified'

# 确保分类结果输出目录存在
os.makedirs(classified_images_output_dir, exist_ok=True)
print(f"分类结果将保存到 '{classified_images_output_dir}' 目录下。")

# 为每个类别创建子文件夹
for class_name in class_names:
    os.makedirs(os.path.join(classified_images_output_dir, class_name), exist_ok=True)

# --- 7. 遍历并分类所有裁剪图片 ---
print(f"\n--- 开始分类 '{target_classification_dir}' 目录下的图片 ---")
total_processed = 0
total_skipped = 0

# 遍历指定的分类目标目录
if not os.path.exists(target_classification_dir):
    print(f"错误：目标分类目录 '{target_classification_dir}' 不存在。请先运行裁剪脚本生成图片。")
    exit()

# 预过滤出所有要处理的图片文件，以便 tqdm 能够知道总数
image_files_to_classify = [f for f in os.listdir(target_classification_dir)
                           if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.tif'))]

# 使用 tqdm 包装循环，添加描述信息
for file in tqdm(image_files_to_classify, desc=f"分类进度 ({target_classification_dir})"):
    image_path = os.path.join(target_classification_dir, file)

    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            # tqdm 会自动处理跳过的迭代，但我们仍然需要记录并打印警告
            tqdm.write(f"警告：无法读取图片 '{image_path}'。跳过。")
            total_skipped += 1
            continue

        # OpenCV 读取是 BGR 格式，需要转换为 RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # img_rgb 是 uint8 类型，范围 [0, 255]

        # 预处理图片并转换为 Tensor
        # transforms.ToTensor() 会自动将 uint8 [0, 255] 转换为 float32 [0.0, 1.0]
        # 且会调整维度为 CHW。
        input_tensor = transform(img_rgb).unsqueeze(0)  # 添加 batch 维度
        input_tensor = input_tensor.to(device)

        # 推理
        with torch.no_grad():  # 推理时禁用梯度计算，节省内存和加速
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_index = torch.argmax(probabilities, dim=1).item()
            predicted_class_name = class_names[predicted_index]
            confidence = probabilities[0][predicted_index].item() * 100

        # 复制图片到对应的分类文件夹
        destination_folder = os.path.join(classified_images_output_dir, predicted_class_name)
        classified_filename = os.path.basename(image_path)  # 使用原始随机文件名
        destination_path = os.path.join(destination_folder, classified_filename)

        shutil.copy2(image_path, destination_path)  # 复制文件，并保留元数据
        # 如果需要更详细的每文件进度，可以在这里取消注释，但 tqdm 通常已足够
        # tqdm.write(f"已分类 '{file}' 为 '{predicted_class_name}' (置信度: {confidence:.2f}%)")
        total_processed += 1

    except Exception as e:
        tqdm.write(f"处理文件 '{image_path}' 时发生错误: {e}. 跳过此文件。")
        total_skipped += 1
        continue

print(f"\n--- 分类完成 ---")
print(f"总共处理了 {total_processed} 张图片。")
print(f"跳过了 {total_skipped} 张图片 (因无法读取或错误)。")