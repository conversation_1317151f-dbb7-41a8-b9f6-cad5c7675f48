import cv2
import os
import shutil
import random
import string
import numpy as np
from PIL import Image
from tqdm import tqdm
import torchvision.transforms as transforms


# --- 辅助函数：生成随机文件名 ---
def generate_random_filename(length=9, extension='.jpg'):
    """生成指定长度的随机字母文件名。"""
    letters = string.ascii_lowercase
    random_name = ''.join(random.choice(letters) for i in range(length))
    return random_name + extension


# --- 自定义半透明覆盖增强 ---
class AddSemiTransparentOverlay(object):
    def __init__(self, alpha_range=(0.1, 0.4), color_range=(0, 255), p=0.5):
        """
        初始化半透明覆盖增强
        :param alpha_range: 透明度范围 (0.0-1.0)，表示覆盖层的不透明度。0为完全透明，1为完全不透明。
        :param color_range: RGB颜色通道的随机范围 (0-255)。
        :param p: 应用增强的概率。
        """
        self.alpha_min, self.alpha_max = alpha_range
        self.color_min, self.color_max = color_range
        self.p = p

    def __call__(self, img):
        if random.random() < self.p:
            img_np = np.array(img)  # PIL Image 转 NumPy (RGB)

            # 随机生成一个颜色 (RGB)
            r = random.randint(self.color_min, self.color_max)
            g = random.randint(self.color_min, self.color_max)
            b = random.randint(self.color_min, self.color_max)
            overlay_color = np.array([r, g, b], dtype=np.uint8)

            # 创建与图像大小相同的纯色覆盖层
            overlay = np.full(img_np.shape, overlay_color, dtype=np.uint8)

            # 随机选择透明度
            alpha = random.uniform(self.alpha_min, self.alpha_max)

            # 图像混合: result = alpha * overlay + (1 - alpha) * original
            # cv2.addWeighted 函数要求输入为浮点类型进行混合，然后可以转换回uint8
            blended_img_np = cv2.addWeighted(overlay, alpha, img_np, 1 - alpha, 0)

            return Image.fromarray(blended_img_np)  # NumPy (RGB) 转回 PIL Image
        else:
            return img  # 不应用增强


# --- 图像增强定义：保持原有尺寸，定义多种增强组合 ---

# 前置处理：确保所有增强都从 PIL Image 开始
# (因为 cv2.imread 返回 NumPy，而 torchvision.transforms 期望 PIL Image)
pil_convert_transform = transforms.ToPILImage()

# 1. 只有水平翻转 (p=1.0 确保每次都翻转，这样能固定增加一倍数据)
flip_only_transform = transforms.Compose([
    pil_convert_transform,  # 将 numpy 转换为 PIL
    transforms.RandomHorizontalFlip(p=1.0),  # 100% 概率水平翻转
])

# 2. 只有色彩抖动 (参数可调)
color_jitter_only_transform = transforms.Compose([
    pil_convert_transform,  # 将 numpy 转换为 PIL
    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
])

# 3. 只有半透明覆盖 (p=1.0 确保每次都覆盖)
overlay_only_transform = transforms.Compose([
    pil_convert_transform,  # 将 numpy 转换为 PIL
    AddSemiTransparentOverlay(alpha_range=(0.1, 0.4), color_range=(0, 255), p=1.0),  # 100% 概率添加半透明覆盖
])

# 4. 组合增强 (保留原有的概率设置)
combined_transform = transforms.Compose([
    pil_convert_transform,  # 将 numpy 转换为 PIL
    transforms.RandomHorizontalFlip(p=0.5),  # 50% 概率水平翻转
    transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
    AddSemiTransparentOverlay(alpha_range=(0.1, 0.4), color_range=(0, 255), p=0.7),
])

# 定义一个列表，存储所有要应用的增强策略
# 每个策略是一个字典，包含：
# - 'name': 增强的名称（用于打印和统计）
# - 'transform': 对应的 transforms.Compose 对象
# - 'generate_new_file': 布尔值，表示是否生成新的增强图片（原始图片是直接复制）
augmentation_strategies = [
    {'name': 'flipped', 'transform': flip_only_transform, 'generate_new_file': True},
    {'name': 'color_jittered', 'transform': color_jitter_only_transform, 'generate_new_file': True},
    {'name': 'overlayed', 'transform': overlay_only_transform, 'generate_new_file': True},
    {'name': 'combined', 'transform': combined_transform, 'generate_new_file': True},
]

# --- 目录设置 ---
input_base_dir = 'Classified'  # 原始数据集路径
output_base_dir = 'Addition_Classified'  # 增强后数据集保存路径

# 类别列表 (与你的模型类别一致)
class_names = ['tk', 'fj', 'ylb', 'zyb', 'tjb', 'zcb', 'dd', 'sw', 'kb']


# --- 主程序 ---
def main():
    # 确保输入目录存在
    if not os.path.exists(input_base_dir):
        print(f"错误: 原始数据集目录 '{input_base_dir}' 不存在。请确保它位于脚本同级目录。")
        return

    # 每次运行前清空输出目录，确保内容是本次生成的
    if os.path.exists(output_base_dir):
        shutil.rmtree(output_base_dir)  # 删除旧的目录
        print(f"已清空旧的 '{output_base_dir}' 目录。")
    os.makedirs(output_base_dir, exist_ok=True)
    print(f"增强后的数据集（包含原始数据）将保存到 '{output_base_dir}' 目录下。")

    total_originals_copied = 0
    total_augmented_saved = {strategy['name']: 0 for strategy in augmentation_strategies}

    for class_name in class_names:
        input_class_dir = os.path.join(input_base_dir, class_name)
        output_class_dir = os.path.join(output_base_dir, class_name)

        # 确保输出类别文件夹存在
        os.makedirs(output_class_dir, exist_ok=True)

        if not os.path.exists(input_class_dir):
            print(f"警告: 原始类别文件夹 '{input_class_dir}' 不存在，跳过。")
            continue

        # 获取当前类别文件夹中的所有图片文件
        image_files = [f for f in os.listdir(input_class_dir)
                       if os.path.isfile(os.path.join(input_class_dir, f)) and \
                       f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.tif'))]

        if not image_files:
            print(f"'{input_class_dir}' 目录下没有图片需要增强。")
            continue

        # 使用 tqdm 显示进度
        for file_name in tqdm(image_files, desc=f"处理类别: {class_name}"):
            original_image_path = os.path.join(input_class_dir, file_name)

            try:
                # --- 1. 复制原始图片到 Addition_Classified ---
                original_copy_path = os.path.join(output_class_dir, file_name)
                shutil.copy2(original_image_path, original_copy_path)
                total_originals_copied += 1

                # 读取原始图片 (OpenCV默认是BGR)
                img_bgr_original = cv2.imread(original_image_path)
                if img_bgr_original is None:
                    tqdm.write(f"警告: 无法读取图片 '{original_image_path}'。跳过所有增强。")
                    continue

                # 将 BGR 转换为 RGB，因为 PIL 和 torchvision 期望 RGB
                # img_rgb_original 将作为所有增强的起始点
                img_rgb_original = cv2.cvtColor(img_bgr_original, cv2.COLOR_BGR2RGB)

                # --- 2. 应用所有定义的增强策略并保存 ---
                for strategy in augmentation_strategies:
                    if strategy['generate_new_file']:
                        try:
                            # 应用数据增强变换
                            # 原始图片 (NumPy array) 传入 transform 链，它会先转换为 PIL Image
                            augmented_pil_img = strategy['transform'](img_rgb_original)

                            # 将增强后的 PIL Image 转换回 NumPy 数组 (RGB)
                            augmented_np_rgb = np.array(augmented_pil_img)

                            # 将 RGB 转换回 BGR 以便使用 cv2.imwrite 保存
                            augmented_np_bgr = cv2.cvtColor(augmented_np_rgb, cv2.COLOR_RGB2BGR)

                            # 生成一个新的随机文件名并保存增强后的图片
                            new_file_name = generate_random_filename(extension='.jpg')
                            save_path = os.path.join(output_class_dir, new_file_name)
                            cv2.imwrite(save_path, augmented_np_bgr)
                            total_augmented_saved[strategy['name']] += 1
                        except Exception as e_aug:
                            tqdm.write(f"警告: 应用 '{strategy['name']}' 增强到 '{file_name}' 时发生错误: {e_aug}")
                            continue  # 继续尝试其他增强

            except Exception as e:
                tqdm.write(f"处理文件 '{original_image_path}' 时发生整体错误: {e}")
                continue

    print(f"\n所有类别的数据增强已完成。")
    print(f"原始图片已复制到 '{output_base_dir}'：{total_originals_copied} 张。")
    for name, count in total_augmented_saved.items():
        print(f"  - '{name}' 增强后的图片已生成：{count} 张。")


if __name__ == "__main__":
    main()