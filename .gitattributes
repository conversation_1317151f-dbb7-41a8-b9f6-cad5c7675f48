完整的排列已生成
    if (k == n_perm) {
            current_permutation[k] = '\0'; // 添加字符串结束符
                    printf("%s\n", current_permutation);
                            permutation_count++;
                                    return;
                                        }

                                            // 尝试将原始字符放入当前位置 current_permutation[k]
                                                for (int i = 0; i < n_perm; ++i) {
                                                        // 如果 original_s[i] 已经被用过了，则跳过
                                                                if (used[i]) {
                                                                            continue;
                                                                                    }

                                                                                            // 去重关键：
                                                                                                    // 如果当前字符 original_s[i] 与其前一个字符 original_s[i-1] 相同 (前提是 i > 0)
                                                                                                            // 并且前一个相同的字符 original_s[i-1] 还没有被使用 (used[i-1] == false)
                                                                                                                    // 那么就跳过当前的 original_s[i]，以避免重复。
                                                                                                                            // 这是因为我们强制要求对于相同的字符，总是先使用在排序后数组中下标较小的那个。
                                                                                                                                    // 如果下标较小的那个还没用，那现在也不应该用下标较大的这个。
                                                                                                                                            if (i > 0 && original_s[i] == original_s[i - 1] && !used[i - 1]) {
                                                                                                                                                        continue;
                                                                                                                                                                }

                                                                                                                                                                        used[i] = true;                     // 标记 original_s[i] 已使用
                                                                                                                                                                                current_permutation[k] = original_s[i]; // 将字符放入当前排列的第 k 位
                                                                                                                                                                                        generate_permutations(k + 1);       // 递归填充下一个位置
                                                                                                                                                                                                used[i] = false;                    // 回溯：撤销选择，original_s[i] 恢复为未使用状态
                                                                                                                                                                                                    }
                                                                                                                                                                                                    }

                                                                                                                                                                                                    int main() {
                                                                                                                                                                                                        while (scanf("%d", &n_perm) != EOF) { //循环处理多组测试数据
                                                                                                                                                                                                                scanf("%s", original_s); // 读取待排列的n个元素

                                                                                                                                                                                                                        // 对原始输入的字符进行排序，这是去重算法的前提
                                                                                                                                                                                                                                qsort(original_s, n_perm, sizeof(char), char_compare);

                                                                                                                                                                                                                                        permutation_count = 0; // 初始化排列计数器
                                                                                                                                                                                                                                                for (int i = 0; i < n_perm; ++i) {
                                                                                                                                                                                                                                                            used[i] = false; // 初始化 used 数组
                                                                                                                                                                                                                                                                    }

                                                                                                                                                                                                                                                                            generate_permutations(0); // 从第0个位置开始生成排列
                                                                                                                                                                                                                                                                                    printf("%lld\n", permutation_count); // 输出排列总数
                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                            return 0;
                                                                                                                                                                                                                                                                                            }#include <stdio.h>
                                                                                                                                                                                                                                                                                            #include <string.h>
                                                                                                                                                                                                                                                                                            #include <stdlib.h> // 用于 qsort
                                                                                                                                                                                                                                                                                            #include <stdbool.h> // 用于 bool 类型

                                                                                                                                                                                                                                                                                            char original_s[501];       // 存储原始输入的字符数组
                                                                                                                                                                                                                                                                                            char current_permutation[501]; // 存储当前正在构建的排列
                                                                                                                                                                                                                                                                                            bool used[501];             // 标记原始字符是否已被用于当前排列
                                                                                                                                                                                                                                                                                            int n_perm;                 // 元素的个数
                                                                                                                                                                                                                                                                                            long long permutation_count; // 排列的总数

                                                                                                                                                                                                                                                                                            // qsort 使用的比较函数，用于字符升序排序
                                                                                                                                                                                                                                                                                            int char_compare(const void *a, const void *b) {
                                                                                                                                                                                                                                                                                                return (*(char *)a - *(char *)b);
                                                                                                                                                                                                                                                                                                }

                                                                                                                                                                                                                                                                                                // 递归函数，生成排列
                                                                                                                                                                                                                                                                                                // k 表示当前正在填充 current_permutation 的第 k 个位置 (从0开始)
                                                                                                                                                                                                                                                                                                void generate_permutations(int k) {
                                                                                                                                                                                                                                                                                                    // 基本情况：如果已经填满了 n_perm 个位置，说明一个完整的排列已生成
                                                                                                                                                                                                                                                                                                        if (k == n_perm) {
                                                                                                                                                                                                                                                                                                                current_permutation[k] = '\0'; // 添加字符串结束符
                                                                                                                                                                                                                                                                                                                        printf("%s\n", current_permutation);
                                                                                                                                                                                                                                                                                                                                permutation_count++;
                                                                                                                                                                                                                                                                                                                                        return;
                                                                                                                                                                                                                                                                                                                                            }

                                                                                                                                                                                                                                                                                                                                                // 尝试将原始字符放入当前位置 current_permutation[k]
                                                                                                                                                                                                                                                                                                                                                    for (int i = 0; i < n_perm; ++i) {
                                                                                                                                                                                                                                                                                                                                                            // 如果 original_s[i] 已经被用过了，则跳过
                                                                                                                                                                                                                                                                                                                                                                    if (used[i]) {
                                                                                                                                                                                                                                                                                                                                                                                continue;
                                                                                                                                                                                                                                                                                                                                                                                        }

                                                                                                                                                                                                                                                                                                                                                                                                // 去重关键：
                                                                                                                                                                                                                                                                                                                                                                                                        // 如果当前字符 original_s[i] 与其前一个字符 original_s[i-1] 相同 (前提是 i > 0)
                                                                                                                                                                                                                                                                                                                                                                                                                // 并且前一个相同的字符 original_s[i-1] 还没有被使用 (used[i-1] == false)
                                                                                                                                                                                                                                                                                                                                                                                                                        // 那么就跳过当前的 original_s[i]，以避免重复。
                                                                                                                                                                                                                                                                                                                                                                                                                                // 这是因为我们强制要求对于相同的字符，总是先使用在排序后数组中下标较小的那个。
                                                                                                                                                                                                                                                                                                                                                                                                                                        // 如果下标较小的那个还没用，那现在也不应该用下标较大的这个。
                                                                                                                                                                                                                                                                                                                                                                                                                                                if (i > 0 && original_s[i] == original_s[i - 1] && !used[i - 1]) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                            continue;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }

                                                                                                                                                                                                                                                                                                                                                                                                                                                                            used[i] = true;                     // 标记 original_s[i] 已使用
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    current_permutation[k] = original_s[i]; // 将字符放入当前排列的第 k 位
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            generate_permutations(k + 1);       // 递归填充下一个位置
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    used[i] = false;                    // 回溯：撤销选择，original_s[i] 恢复为未使用状态
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        int main() {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            while (scanf("%d", &n_perm) != EOF) { //循环处理多组测试数据
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    scanf("%s", original_s); // 读取待排列的n个元素

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 对原始输入的字符进行排序，这是去重算法的前提
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    qsort(original_s, n_perm, sizeof(char), char_compare);

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            permutation_count = 0; // 初始化排列计数器
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for (int i = 0; i < n_perm; ++i) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                used[i] = false; // 初始化 used 数组
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                generate_permutations(0); // 从第0个位置开始生成排列
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        printf("%lld\n", permutation_count); // 输出排列总数
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return 0;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                }#include <stdio.h>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                #include <string.h>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                #include <stdlib.h> // 用于 qsort
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                #include <stdbool.h> // 用于 bool 类型

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                char original_s[501];       // 存储原始输入的字符数组
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                char current_permutation[501]; // 存储当前正在构建的排列
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                bool used[501];             // 标记原始字符是否已被用于当前排列
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                int n_perm;                 // 元素的个数
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                long long permutation_count; // 排列的总数

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                // qsort 使用的比较函数，用于字符升序排序
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                int char_compare(const void *a, const void *b) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    return (*(char *)a - *(char *)b);
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }# Auto detect text files and perform LF normalization
* text=auto
