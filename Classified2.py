import cv2
import os
import shutil  # 用于文件操作 (copy, move)
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image  # 用于 torchvision.transforms (Image类)
import string  # 用于 generate_random_filename
from tqdm import tqdm  # 导入 tqdm 库
import random

# --- 辅助函数：生成随机文件名 (沿用之前脚本的，这里不再生成新名，但函数保留) ---
def generate_random_filename(length=9, extension='.jpg'):
    """生成指定长度的随机字母文件名。"""
    letters = string.ascii_lowercase
    random_name = ''.join(random.choice(letters) for i in range(length))
    return random_name + extension


# --- 1. 复制你的CNN模型架构 (与训练代码完全一致!) ---
class BattlefieldCNN(nn.Module):
    def __init__(self, num_classes=9):
        super(BattlefieldCNN, self).__init__()
        self.features = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2)
        )
        self.classifier = nn.Sequential(
            nn.Flatten(),
            # 确保这里的线性层输入维度与模型结构和输入尺寸相匹配
            # 14x14 -> Conv(padding=1) -> 16x14x14 -> MaxPool(2) -> 16x7x7
            # -> Conv(padding=1) -> 32x7x7 -> MaxPool(2) -> 32x3x3
            # 展平后为 32 * 3 * 3 = 288
            nn.Linear(32 * 3 * 3, 64),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x


# --- 2. 模型路径和类别映射 ---
model_path = 'battlefield_model.pth'  # 确保模型文件在脚本同目录下
# 类别映射：顺序必须与你模型训练时的输出顺序一致
class_names = ['tk', 'fj', 'ylb', 'zyb', 'tjb', 'zcb', 'dd', 'sw', 'kb']

# --- 3. 图像预处理定义 (与训练时保持一致) ---
# 训练时图片被 resize 为 14x14 (宽x高)
input_image_size = (14, 14)  # (width, height) for cv2.resize, (height, width) for transforms.Resize

transform = transforms.Compose([
    transforms.ToPILImage(),  # OpenCV 读取是 NumPy 数组，需要转换为 PIL Image
    transforms.Resize(input_image_size),  # 强制 Resize 到 14x14
    transforms.ToTensor(),  # 将 PIL Image (0-255) 转换为 Tensor (0.0-1.0), 并调整维度为 CHW
    # 注意: 训练代码实际并未应用 transforms.Normalize(mean=[0.5,0.5,0.5], std=[0.5,0.5,0.5])
    # 仅进行了 /255.0 归一化。ToTensor() 已经实现了 [0,1] 归一化。
])

# --- 4. 设置设备 (GPU 如果可用，否则 CPU) ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"将使用设备: {device}")

# --- 5. 加载模型 ---
try:
    # 实例化模型
    model = BattlefieldCNN(num_classes=len(class_names))
    # 将模型加载到正确的设备上
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()  # 设置为评估模式 (禁用 dropout, batch norm 等)
    print(f"模型 '{model_path}' 加载成功。")
except Exception as e:
    print(f"错误：加载模型失败。请确保模型路径正确，并且 'BattlefieldCNN' 的定义与 '{model_path}' 中的模型架构完全一致。")
    print(f"详细错误: {e}")
    exit()

# --- 6. 输入输出目录设置 ---
# 分类结果的根目录名称
classified_images_root_dir = 'Classified'

# 指定要进行二次筛选的源目录 (Classified/zcb)
target_reclassification_dir = os.path.join(classified_images_root_dir, 'zcb')

# 定义所有不符合高置信度 'zcb' 条件的图片的目标文件夹 (Classified/zcb/low)
low_confidence_destination_dir = os.path.join(target_reclassification_dir, 'low')

print(f"将对 '{target_reclassification_dir}' 目录下的图片进行二次筛选。")

# 确保所有可能的分类结果的子文件夹都存在 (用于统计，即使不被 zcb_high_conf 分类，也会被移动)
# 注意：这里不再需要为所有 class_names 创建文件夹，因为除了 zcb 外，其他图片都会移到 zcb/low
# 但是为了统计方便，我们可以假设这些文件夹存在，或者在需要时动态创建。
# 实际操作中，只需要确保 zcb_low_confidence_dir 存在。
os.makedirs(low_confidence_destination_dir, exist_ok=True)  # 确保 'Classified/zcb/low' 文件夹存在

# --- 7. 遍历并分类所有裁剪图片 ---
print(f"\n--- 开始二次筛选和分类 '{target_reclassification_dir}' 目录下的图片 ---")

# 用于统计结果
total_processed = 0
total_skipped = 0
kept_in_zcb_high_conf = 0  # 留在 Classified/zcb 的图片
moved_to_zcb_low = 0  # 移到 Classified/zcb/low 的图片

# 遍历指定的分类目标目录
if not os.path.exists(target_reclassification_dir):
    print(f"错误：目标筛选目录 '{target_reclassification_dir}' 不存在。请先运行分类脚本。")
    exit()

# 预过滤出所有要处理的图片文件，以便 tqdm 能够知道总数
# 排除 'low' 子文件夹本身，以避免递归处理
image_files_to_reclassify = [f for f in os.listdir(target_reclassification_dir)
                             if os.path.isfile(os.path.join(target_reclassification_dir, f)) and \
                             f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.tif'))]

if not image_files_to_reclassify:
    print(f"'{target_reclassification_dir}' 目录下没有图片需要筛选。")
    print("--- 筛选完成 ---")
    exit()

# 使用 tqdm 包装循环，添加描述信息
for file in tqdm(image_files_to_reclassify, desc=f"筛选进度 ({target_reclassification_dir})"):
    image_path = os.path.join(target_reclassification_dir, file)  # 当前图片在zcb目录下的完整路径

    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            tqdm.write(f"警告：无法读取图片 '{image_path}'。跳过。")
            total_skipped += 1
            continue

        # OpenCV 读取是 BGR 格式，需要转换为 RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # 预处理图片并转换为 Tensor
        input_tensor = transform(img_rgb).unsqueeze(0)  # 添加 batch 维度
        input_tensor = input_tensor.to(device)

        # 推理
        with torch.no_grad():
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            predicted_index = torch.argmax(probabilities, dim=1).item()
            predicted_class_name = class_names[predicted_index]
            confidence = probabilities[0][predicted_index].item() * 100

        # --- 二次筛选和移动逻辑 ---
        if predicted_class_name == 'zcb' and confidence >= 90.0:
            # 高置信度 zcb：图片符合条件，留在 Classified/zcb
            kept_in_zcb_high_conf += 1
            # 无需移动，因为它已经在目标位置
            # tqdm.write(f"保留 '{file}' 在 'zcb' (置信度: {confidence:.2f}%)")
        else:
            # 不符合高置信度 zcb 条件：移动到 Classified/zcb/low
            destination_path = os.path.join(low_confidence_destination_dir, file)

            # 只有当最终目标路径与原路径不同时才执行移动操作
            # 这里是必要的，因为如果文件已经在 Classified/zcb/low 里，它不会再被处理到
            # 但如果 Classified/zcb/low 不存在， os.path.isfile(os.path.join(target_reclassification_dir, f))
            # 就会排除它。如果处理前 Classified/zcb/low 存在且里面有文件，那些文件就不会被处理到。
            # 这是为了确保不会出现 shutil.move(file, file) 的情况。
            shutil.move(image_path, destination_path)
            moved_to_zcb_low += 1
            tqdm.write(
                f"'{file}' (预测为: {predicted_class_name}, 置信度: {confidence:.2f}%) 移到 '{low_confidence_destination_dir}'")

        total_processed += 1

    except Exception as e:
        tqdm.write(f"处理文件 '{image_path}' 时发生错误: {e}. 跳过此文件。")
        total_skipped += 1
        continue

print(f"\n--- 筛选完成 ---")
print(f"总共尝试处理了 {total_processed} 张图片。")
print(f"  - 留在 'Classified/zcb' (高置信度 'zcb'): {kept_in_zcb_high_conf} 张")
print(f"  - 移到 '{low_confidence_destination_dir}' 的图片: {moved_to_zcb_low} 张")
print(f"跳过了 {total_skipped} 张图片 (因无法读取或错误)。")